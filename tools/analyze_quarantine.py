#!/usr/bin/env python3
# tools/analyze_quarantine.py

"""
Аналитический инструмент для работы с карантином PostgreSQL.

Новая архитектура "Аудиторский след":
- Анализ записей в таблице quarantined_books
- Статистика по типам карантина
- Поиск и фильтрация проблемных книг
- Экспорт данных для дальнейшего анализа
- Возможность чтения исходных файлов через details JSONB
"""

import argparse
import csv
import json
import logging
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Optional

import psycopg

# Добавляем корневую директорию в путь для импорта модулей приложения
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.database.connection import get_db_connection
from app.storage.local import LocalStorageManager

logger = logging.getLogger(__name__)


class QuarantineAnalyzer:
    """Аналитический инструмент для карантина PostgreSQL."""

    def __init__(self):
        self.storage_manager = LocalStorageManager()

    def get_quarantine_stats(self, days: Optional[int] = None) -> dict[str, Any]:
        """Получает общую статистику карантина.

        Args:
            days: Ограничить анализ последними N днями

        Returns:
            Словарь со статистикой
        """
        try:
            with get_db_connection() as conn:
                with conn.cursor() as cur:
                    # Базовый запрос
                    where_clause = ""
                    params = []

                    if days:
                        where_clause = "WHERE updated_at >= %s"
                        params.append(datetime.now() - timedelta(days=days))

                    # Общая статистика
                    # nosec B608 - where_clause формируется безопасно
                    cur.execute(
                        f"""
                        SELECT
                            COUNT(*) as total_books,
                            COUNT(DISTINCT details->>'archive_path') as unique_archives,
                            MIN(updated_at) as first_quarantine,
                            MAX(updated_at) as last_quarantine
                        FROM quarantined_books
                        {where_clause}
                    """,
                        params,
                    )

                    general_stats = dict(cur.fetchone())

                    # Статистика по типам
                    # nosec B608 - where_clause формируется безопасно
                    cur.execute(
                        f"""
                        SELECT
                            primary_quarantine_type,
                            COUNT(*) as count,
                            ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
                        FROM quarantined_books
                        {where_clause}
                        GROUP BY primary_quarantine_type
                        ORDER BY count DESC
                    """,
                        params,
                    )

                    type_stats = [dict(row) for row in cur.fetchall()]

                    return {
                        "general": general_stats,
                        "by_type": type_stats,
                        "analysis_period": f"last {days} days" if days else "all time",
                    }

        except psycopg.Error as e:
            logger.error(f"Ошибка получения статистики: {e}")
            raise

    def search_quarantine(
        self,
        quarantine_type: Optional[str] = None,
        archive_path: Optional[str] = None,
        reason_pattern: Optional[str] = None,
        limit: int = 100,
    ) -> list[dict[str, Any]]:
        """Поиск записей в карантине по различным критериям.

        Args:
            quarantine_type: Фильтр по типу карантина
            archive_path: Фильтр по пути архива (поддерживает LIKE)
            reason_pattern: Поиск по тексту причины (поддерживает LIKE)
            limit: Максимальное количество результатов

        Returns:
            Список найденных записей
        """
        try:
            with get_db_connection() as conn:
                with conn.cursor() as cur:
                    conditions = []
                    params = []

                    if quarantine_type:
                        conditions.append("primary_quarantine_type = %s")
                        params.append(quarantine_type)

                    if archive_path:
                        conditions.append("details->>'archive_path' LIKE %s")
                        params.append(f"%{archive_path}%")

                    if reason_pattern:
                        conditions.append("reason LIKE %s")
                        params.append(f"%{reason_pattern}%")

                    where_clause = ""
                    if conditions:
                        where_clause = "WHERE " + " AND ".join(conditions)

                    # nosec B608 - where_clause формируется безопасно
                    cur.execute(
                        f"""
                        SELECT
                            source_type,
                            source_id,
                            primary_quarantine_type,
                            reason,
                            details,
                            updated_at
                        FROM quarantined_books
                        {where_clause}
                        ORDER BY updated_at DESC
                        LIMIT %s
                    """,
                        params + [limit],
                    )

                    return [dict(row) for row in cur.fetchall()]

        except psycopg.Error as e:
            logger.error(f"Ошибка поиска в карантине: {e}")
            raise

    def get_top_problematic_archives(self, limit: int = 20) -> list[dict[str, Any]]:
        """Получает архивы с наибольшим количеством проблемных книг.

        Args:
            limit: Количество архивов в результате

        Returns:
            Список архивов с статистикой
        """
        try:
            with get_db_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        SELECT 
                            details->>'archive_path' as archive_path,
                            COUNT(*) as books_count,
                            array_agg(DISTINCT primary_quarantine_type) as quarantine_types,
                            MIN(updated_at) as first_issue,
                            MAX(updated_at) as last_issue
                        FROM quarantined_books 
                        WHERE details->>'archive_path' IS NOT NULL
                        GROUP BY details->>'archive_path'
                        HAVING COUNT(*) > 1
                        ORDER BY books_count DESC
                        LIMIT %s
                    """,
                        (limit,),
                    )

                    return [dict(row) for row in cur.fetchall()]

        except psycopg.Error as e:
            logger.error(f"Ошибка получения проблемных архивов: {e}")
            raise

    def export_to_csv(self, data: list[dict[str, Any]], output_file: Path) -> None:
        """Экспортирует данные в CSV файл.

        Args:
            data: Данные для экспорта
            output_file: Путь к выходному файлу
        """
        if not data:
            logger.warning("Нет данных для экспорта")
            return

        try:
            with open(output_file, "w", newline="", encoding="utf-8") as csvfile:
                fieldnames = data[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for row in data:
                    # Преобразуем сложные типы в строки для CSV
                    csv_row = {}
                    for key, value in row.items():
                        if isinstance(value, (dict, list)):
                            csv_row[key] = json.dumps(value, ensure_ascii=False)
                        elif isinstance(value, datetime):
                            csv_row[key] = value.isoformat()
                        else:
                            csv_row[key] = value
                    writer.writerow(csv_row)

            logger.info(f"Данные экспортированы в {output_file}")

        except Exception as e:
            logger.error(f"Ошибка экспорта в CSV: {e}")
            raise

    def read_quarantined_file(self, source_type: int, source_id: int) -> Optional[bytes]:
        """Читает исходный файл из карантина по его координатам.

        Args:
            source_type: Тип источника
            source_id: ID источника

        Returns:
            Содержимое файла или None если не найден
        """
        try:
            with get_db_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        SELECT details 
                        FROM quarantined_books 
                        WHERE source_type = %s AND source_id = %s
                    """,
                        (source_type, source_id),
                    )

                    result = cur.fetchone()
                    if not result:
                        logger.warning(f"Запись карантина не найдена: {source_type}:{source_id}")
                        return None

                    result_dict = dict(result)
                    details = result_dict["details"]
                    archive_path = details.get("archive_path")
                    book_filename = details.get("book_filename")

                    if not archive_path or not book_filename:
                        logger.warning(f"Недостаточно данных для чтения файла: {details}")
                        return None

                    # Читаем файл через StorageManager
                    file_stream = self.storage_manager.read_file_from_archive(
                        archive_path=archive_path, file_in_archive=book_filename
                    )
                    return file_stream.getvalue() if file_stream else None

        except Exception as e:
            logger.error(f"Ошибка чтения файла из карантина: {e}")
            return None


def setup_logging(debug: bool = False):
    """Настройка логирования."""
    level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(level=level, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")


def main():
    """Главная функция CLI."""
    parser = argparse.ArgumentParser(
        description="Анализ карантина PostgreSQL",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Примеры использования:
  %(prog)s --stats                              # Общая статистика
  %(prog)s --type trial --limit 10              # Первые 10 trial записей
  %(prog)s --archive-path "problematic.zip"     # Поиск по архиву
  %(prog)s --reason "фрагмент" --export-csv     # Экспорт в CSV
  %(prog)s --top-archives 20                    # Топ проблемных архивов
        """,
    )

    parser.add_argument("--stats", action="store_true", help="Показать общую статистику карантина")
    parser.add_argument("--type", help="Фильтр по типу карантина")
    parser.add_argument("--archive-path", help="Фильтр по пути архива (поддерживает частичное совпадение)")
    parser.add_argument("--reason", help="Поиск по тексту причины")
    parser.add_argument("--limit", type=int, default=100, help="Максимальное количество результатов")
    parser.add_argument("--days", type=int, help="Ограничить анализ последними N днями")
    parser.add_argument("--top-archives", type=int, help="Показать топ проблемных архивов")
    parser.add_argument("--export-csv", help="Экспортировать результаты в CSV файл")
    parser.add_argument("--read-file", help="Прочитать файл из карантина (формат: source_type:source_id)")
    parser.add_argument("--debug", action="store_true", help="Включить отладочный вывод")

    args = parser.parse_args()

    setup_logging(args.debug)

    analyzer = QuarantineAnalyzer()

    try:
        if args.stats:
            # Показать статистику
            stats = analyzer.get_quarantine_stats(args.days)

            print("📊 Статистика карантина")
            print("=" * 50)
            print(f"Период анализа: {stats['analysis_period']}")
            print(f"Всего книг в карантине: {stats['general']['total_books']:,}")
            print(f"Уникальных архивов: {stats['general']['unique_archives']:,}")

            if stats["general"]["first_quarantine"]:
                print(f"Первая запись: {stats['general']['first_quarantine']}")
                print(f"Последняя запись: {stats['general']['last_quarantine']}")

            print("\nРаспределение по типам:")
            for type_stat in stats["by_type"]:
                print(f"  {type_stat['primary_quarantine_type']}: {type_stat['count']:,} ({type_stat['percentage']}%)")

        elif args.top_archives:
            # Показать топ проблемных архивов
            archives = analyzer.get_top_problematic_archives(args.top_archives)

            print(f"📁 Топ {len(archives)} проблемных архивов")
            print("=" * 80)

            for archive in archives:
                print(f"\n📦 {archive['archive_path']}")
                print(f"   Проблемных книг: {archive['books_count']}")
                print(f"   Типы проблем: {', '.join(archive['quarantine_types'])}")
                print(f"   Период: {archive['first_issue']} - {archive['last_issue']}")

        elif args.read_file:
            # Прочитать файл из карантина
            try:
                source_type, source_id = map(int, args.read_file.split(":"))
                content = analyzer.read_quarantined_file(source_type, source_id)

                if content:
                    print(f"📖 Файл {source_type}:{source_id} прочитан ({len(content)} байт)")
                    # Можно добавить дополнительную обработку содержимого
                else:
                    print(f"❌ Файл {source_type}:{source_id} не найден или недоступен")

            except ValueError:
                print("❌ Неверный формат. Используйте: source_type:source_id (например, 1:12345)")
                sys.exit(1)

        else:
            # Поиск записей
            results = analyzer.search_quarantine(
                quarantine_type=args.type, archive_path=args.archive_path, reason_pattern=args.reason, limit=args.limit
            )

            print(f"🔍 Найдено записей: {len(results)}")
            print("=" * 80)

            for record in results:
                print(f"\n📋 {record['source_type']}:{record['source_id']} ({record['primary_quarantine_type']})")
                print(f"   Причина: {record['reason']}")
                print(f"   Архив: {record['details'].get('archive_path', 'N/A')}")
                print(f"   Файл: {record['details'].get('book_filename', 'N/A')}")
                print(f"   Дата: {record['updated_at']}")

            # Экспорт в CSV если запрошен
            if args.export_csv:
                output_file = Path(args.export_csv)
                analyzer.export_to_csv(results, output_file)

    except Exception as e:
        logger.error(f"Ошибка выполнения: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
