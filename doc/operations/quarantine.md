# Система карантина "Аудиторский след" - Операционное руководство

> **Новая архитектура:** Карантин как неизменяемая запись о событии в PostgreSQL. Исходные файлы остаются нетронутыми.

## 📋 Обзор системы

Система карантина "Аудиторский след" обеспечивает надежное отслеживание проблемных книг через PostgreSQL. Карантин - это не физическое место, а **неизменяемая запись о событии** в базе данных.

### Философия новой архитектуры:
- **PostgreSQL - единственный источник правды** о карантинном статусе
- **Никаких файловых операций** - исходные архивы остаются нетронутыми
- **Атомарная запись** в БД через одну транзакцию
- **Аналитическая гибкость** через JSONB детали и SQL запросы
- **Минимальное влияние на производительность** - быстрые индексированные проверки

## 🏗️ Архитектура данных

### Таблица `quarantined_books`

```sql
CREATE TABLE quarantined_books (
    source_type SMALLINT NOT NULL,           -- 1=flibusta, 2=searchfloor, 3=anna
    source_id INT NOT NULL,                  -- ID файла в источнике
    primary_quarantine_type VARCHAR(50),     -- 'trial', 'small_content', 'anthology'
    reason TEXT,                             -- Человекочитаемая причина
    details JSONB,                           -- task_data + detected_anomalies
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (source_type, source_id)    -- Композитный ключ для быстрого поиска
);
```

### Структура JSONB `details`

```json
{
  "archive_path": "/path/to/archive.zip",
  "book_filename": "12345.fb2",
  "archive_mtime": 1640995200.0,
  "detected_anomalies": [
    {
      "type": "trial",
      "reason": "Обнаружен ознакомительный фрагмент"
    },
    {
      "type": "small_content",
      "reason": "Общее количество контента менее 40000 символов"
    }
  ]
}
```

## 📊 Типы карантина

### Приоритет детекторов

Детекторы выстроены в порядке приоритета. Первый сработавший определяет `primary_quarantine_type`:

1. **`trial`** - Ознакомительные фрагменты (высший приоритет)
2. **`small_content`** - Малое количество контента
3. **`anthology`** - Антологии и сборники
4. **`footnotes`** - Проблемы со сносками
5. **`error`** - Системные ошибки обработки

### Описание типов

#### 🚫 `trial` - Ознакомительные фрагменты
- **Причина:** Обнаружены маркеры ознакомительного фрагмента
- **Логика:** Фрагменты нарушают дедупликацию и блокируют загрузку полных версий
- **Действие:** Исходный файл остается на месте, запись в БД

#### 📏 `small_content` - Малое количество контента
- **Причина:** Общее количество контента менее порогового значения
- **Логика:** Книги с недостаточным объемом текста
- **Действие:** Исходный файл остается на месте, запись в БД

#### 📚 `anthology` - Антологии и сборники
- **Причина:** Обнаружена структура антологии/сборника
- **Логика:** Требуют специальной обработки для разделения на отдельные произведения
- **Действие:** Исходный файл остается на месте, запись в БД

#### 📝 `footnotes` - Проблемы со сносками
- **Причина:** Неопределенные ID сносок или структурные аномалии
- **Логика:** Требуют доработки эвристики определения сносок
- **Действие:** Исходный файл остается на месте, запись в БД

#### ❌ `error` - Системные ошибки
- **Причина:** Ошибки парсинга, доступа к файлам, БД и т.д.
- **Логика:** Технические проблемы, требующие анализа
- **Действие:** Исходный файл остается на месте, запись в БД
## 🔧 Техническая реализация

### Поток данных

```mermaid
graph TD
    A[BookProcessor] -->|QuarantineError| B[BookWorker._handle_task_error]
    B --> C[QuarantineProcessor.process]
    C --> D[add_to_quarantine]
    D --> E[PostgreSQL: quarantined_books]

    F[ScannerInventorizer] --> G[_batch_check_quarantine]
    G --> H[get_quarantined_ids]
    H --> E
    H --> I[Фильтрация кандидатов]
```

### Ключевые компоненты

**QuarantineProcessor** - логический процессор карантина
```python
def process(self, task_data: dict, quarantine_type: QuarantineType, reason: str) -> None:
    """Записывает книгу в карантин PostgreSQL."""
    source_type = task_data.get("source_type")
    source_id = task_data.get("source_id")

    details = {
        "archive_path": task_data.get("archive_path"),
        "book_filename": task_data.get("book_filename"),
        "detected_anomalies": [{"type": quarantine_type.value, "reason": reason}]
    }

    add_to_quarantine(source_type, source_id, quarantine_type.value, reason, details)
```

**Функции доступа к данным**
```python
def add_to_quarantine(source_type: int, source_id: int,
                     quarantine_type: str, reason: str, details: dict) -> None:
    """Атомарная запись в карантин с ON CONFLICT DO NOTHING."""

def get_quarantined_ids(candidate_pairs: list[tuple[int, int]]) -> set[tuple[int, int]]:
    """Пакетная проверка карантина для сканера."""
```

### Интеграция в пайплайны

**BookWorker** - обработка QuarantineError
```python
if isinstance(error, QuarantineError):
    self.quarantine_processor.process(task_data, error.quarantine_type, error.message)
```

**ScannerInventorizer** - фильтрация кандидатов
```python
# Шаг 2: Проверка карантина (новый шаг)
candidates_after_quarantine = self._batch_check_quarantine(candidates_after_redis)
```

## 📈 Детекция и критерии

### Детекторы качества

**FragmentDetector** - ознакомительные фрагменты
```python
def is_fragment(self, canonical_book: CanonicalBook) -> bool:
    """Поиск маркеров ознакомительного фрагмента в тексте."""
    # Проверка на ключевые фразы: "ознакомительный фрагмент", "демо-версия" и т.д.
```

**SmallBookDetector** - структурные аномалии
```python
def check_book_structure(self, canonical_book: CanonicalBook) -> QuarantineType | None:
    """Проверка объема контента и структуры книги."""
    total_content = sum(len(ch.content_md) for ch in canonical_book.chapters)
    if total_content < self.min_content_chars:  # по умолчанию 40000
        return QuarantineType.SMALL_CONTENT
```

**AnthologyDetector** - антологии и сборники
```python
def is_anthology(self, canonical_book: CanonicalBook) -> bool:
    """Определение структуры антологии по авторам и главам."""
    # Анализ множественных авторов и структуры глав
```

### Приоритет детекторов в BookProcessor

```python
def _determine_quarantine_type(self, canonical_book: CanonicalBook):
    # Приоритет 1: Ознакомительные фрагменты
    if self.fragment_detector.is_fragment(canonical_book):
        return QuarantineType.TRIAL, reason

    # Приоритет 2: Структурные аномалии
    quarantine_type = self.small_book_detector.check_book_structure(canonical_book)
    if quarantine_type:
        return quarantine_type, reason

    # Приоритет 3: Антологии
    if self.anthology_detector.is_anthology(canonical_book):
        return QuarantineType.ANTHOLOGIES, reason
```

## 🎯 Преимущества новой архитектуры

### Производительность
- **Нет файловых операций** - исходные архивы остаются нетронутыми
- **Быстрые индексированные проверки** в PostgreSQL
- **Пакетные запросы** для оптимизации сканера
- **Минимальное влияние на I/O** системы

### Надежность
- **Атомарные транзакции** - запись в карантин не может быть частичной
- **ON CONFLICT DO NOTHING** - защита от повторных записей
- **Единственный источник правды** - PostgreSQL для всех проверок

### Аналитические возможности
- **JSONB детали** - гибкие запросы по структурированным данным
- **Временные метки** - анализ динамики карантина
- **SQL аналитика** - стандартные инструменты для отчетов

## 🔍 Мониторинг и аналитика

### ⚠️ Особенности обработки FOOTNOTES

**Архитектурное разделение:**

**В ПРОДАКШЕНЕ (BookProcessor):**
- ✅ **Только мониторинг** - broken footnotes логируются как INFO для аналитики
- ✅ **НЕ блокируют обработку** - книги продолжают обрабатываться в основной пайплайн  
- ✅ **Обоснование** - основной контент книги важнее вспомогательных сносок для RAG-системы

**В АНАЛИТИЧЕСКИХ ИНСТРУМЕНТАХ (tools/run_101_*.py):**  
- 📊 **Карантин FOOTNOTES** - для детального изучения проблем извлечения сносок
- 📊 **Цель** - анализ и доработка эвристик парсинга сносок  
- 📊 **Cold Storage стратегия** - сохранение файлов для повторной обработки

```python
# В продакшене: только логирование  
self.logger.info(f"📊 МОНИТОРИНГ: Обнаружены недоступные сноски ({broken_count} шт.)")

# В аналитических инструментах: карантин
if transformer.has_broken_footnotes():
    return QuarantineType.FOOTNOTES, "Требует анализа эвристик извлечения сносок"
```

### Команды проверки

```bash
# Общее количество файлов в карантине
find /sources/*/quarantine/ -name "*.zip" | wc -l

# Статистика по типам (footnotes только в аналитических инструментах)
for type in trial small_content few_chapters anthologies error footnotes invalid; do
    count=$(find /sources/*/quarantine/$type -name "*.zip" 2>/dev/null | wc -l)
    echo "$type: $count файлов"
done

# Размер карантина по папкам  
du -sh /sources/*/quarantine/*/ | sort -h
```

### Метрики для мониторинга

- **Коэффициент карантина** - процент файлов в карантине от общего числа
- **Распределение по типам** - баланс между стратегиями обработки
- **Эффективность сжатия** - экономия места в cold storage
- **Время обработки** - производительность постобработки

## 🚨 Операционные процедуры

### Восстановление из карантина

**COLD_STORAGE файлы:**
```bash
# Файлы можно попробовать переобработать после исправления проблем
mv /sources/X/quarantine/error/book.zip /sources/X/new/
```

**EMPTY_MARKER файлы:**
```bash
# Маркеры содержат информацию о причине, но восстановлению не подлежат
cat /sources/X/quarantine/trial/book.zip  # просмотр _marker_info.txt
```

### Очистка старого карантина

```bash
# Удаление маркеров старше 30 дней
find /sources/*/quarantine/trial/ -name "*.zip" -mtime +30 -delete
find /sources/*/quarantine/invalid/ -name "*.zip" -mtime +30 -delete

# Архивирование cold storage старше 90 дней
find /sources/*/quarantine/error/ -name "*.zip" -mtime +90 -exec tar -czf archive.tar.gz {} \;
```

### Анализ проблемных файлов

```python
# Получение статистики через QuarantineProcessor
processor = QuarantineProcessor()
stats = processor.get_processing_statistics(Path("/sources/X/quarantine"))

print(f"Всего файлов: {stats['total_files']}")
print(f"По стратегиям: {stats['by_strategy']}")
print(f"По типам: {stats['by_type']}")
```

## 🎮 Миграция при изменении типов

При изменении логики карантина (например, переименование `SMALL` → `SMALL_CONTENT`, `FEW_CHAPTERS`):

```python
# Скрипт миграции для перераспределения файлов
# tools/migrate_quarantine_old_to_new.py

# 1. Перепарсивание файлов из старых папок
# 2. Применение новой логики детекции  
# 3. Перемещение в соответствующие новые папки
# 4. Создание отчета о миграции
```

---

**📊 Связанная документация:**
- [Архитектура парсинга](../architecture/parsing_architecture.md) - общие принципы обработки ошибок
- [Архитектурные паттерны](../../ai-docs/architectural-patterns.md) - паттерны стратегий 
- [Redis очереди](../redis_queues.md) - интеграция с системой задач

**🔧 Инструменты:**
- `tools/run_quarantine_demo.py` - демонстрация работы карантина
- `app/processing/quarantine_processor.py` - основная логика
- `app/processing/quarantine_strategies.py` - реестр стратегий 