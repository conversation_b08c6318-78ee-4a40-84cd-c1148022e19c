# app/database/queries.py

import logging
from typing import Any, cast

import psycopg

from app.processing.error_handler import ErrorType, ProcessingError

from .connection import get_db_connection


def is_source_processed(source_type: int, source_id: int) -> bool:
    """ЕДИНСТВЕННЫЙ источник правды о том, обработан ли файл.

    Проверяет наличие (source_type, source_id) в book_sources.
    Использует уникальный индекс uq_source_identity для быстрого поиска.

    ВАЖНО: Все модули должны использовать ТОЛЬКО эту функцию для проверки
    обработанных файлов. Никаких дублирующих проверок по file_path или другим критериям!

    Args:
        source_type: Тип источника (1=flibusta, 2=searchfloor, 3=anna)
        source_id: ID файла (только цифры)

    Returns:
        True если файл уже обработан

    Raises:
        ProcessingError: При ошибках соединения с БД (тип RETRY)

    """
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute(
                    "SELECT 1 FROM book_sources WHERE source_type = %s AND source_id = %s LIMIT 1",
                    (source_type, source_id),
                )
                return cur.fetchone() is not None
    except psycopg.OperationalError as e:
        # Ошибки соединения, блокировки БД - можно повторить
        logging.error(f"❌ Ошибка соединения с БД для source_type={source_type}, source_id={source_id}: {e}")
        raise ProcessingError(
            f"Ошибка соединения с БД при проверке файла {source_type}:{source_id}: {e}",
            error_type=ErrorType.RETRY,
            details={"source_type": source_type, "source_id": source_id, "error_category": "connection"},
        ) from e
    except psycopg.DataError as e:
        # Ошибки данных (неверные параметры) - не повторять
        logging.error(f"❌ Ошибка данных для source_type={source_type}, source_id={source_id}: {e}")
        raise ProcessingError(
            f"Неверные параметры запроса для файла {source_type}:{source_id}: {e}",
            error_type=ErrorType.FATAL,
            details={"source_type": source_type, "source_id": source_id, "error_category": "data"},
        ) from e
    except psycopg.ProgrammingError as e:
        # Ошибки SQL-синтаксиса - критическая ошибка кода
        logging.error(f"❌ Ошибка SQL для source_type={source_type}, source_id={source_id}: {e}")
        raise ProcessingError(
            f"Ошибка SQL-запроса при проверке файла {source_type}:{source_id}: {e}",
            error_type=ErrorType.FATAL,
            details={"source_type": source_type, "source_id": source_id, "error_category": "sql"},
        ) from e
    except psycopg.Error as e:
        # Прочие ошибки psycopg
        logging.error(f"❌ Ошибка БД для source_type={source_type}, source_id={source_id}: {e}")
        raise ProcessingError(
            f"Ошибка БД при проверке файла {source_type}:{source_id}: {e}",
            error_type=ErrorType.RETRY,
            details={"source_type": source_type, "source_id": source_id, "error_category": "database"},
        ) from e


def check_book_duplicates(metadata_hash: str) -> dict[str, str] | None:
    """Проверяет существование книги в БД по metadata_hash.
    Возвращает только сырые данные из БД без бизнес-логики.

    Args:
        metadata_hash: Хэш метаданных книги

    Returns:
        Dict с existing_book_id если найден дубликат, иначе None
    """
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                # Простой запрос по единому хэшу
                cur.execute(
                    "SELECT id FROM books WHERE metadata_hash = %s LIMIT 1",
                    (metadata_hash,),
                )

                result = cur.fetchone()
                if result:
                    row_dict = cast(dict[str, Any], result)
                    return {
                        "existing_book_id": row_dict["id"],
                    }

                # Дубликатов не найдено
                return None

    except psycopg.Error as e:
        logging.error(f"❌ Ошибка проверки дубликатов: {e}")
        raise ProcessingError(
            f"Ошибка БД при проверке дубликатов: {e}",
            error_type=ErrorType.RETRY,
            details={"metadata_hash": metadata_hash, "error_category": "database"},
        ) from e


def get_books_total_count() -> int:
    """Возвращает общее количество книг в БД."""
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("SELECT COUNT(*) as total FROM books")
                result = cur.fetchone()
                row_dict = cast(dict[str, Any], result)
                return row_dict["total"]
    except psycopg.Error as e:
        logging.error(f"❌ Ошибка получения общего количества книг: {e}")
        return 0


def check_data_integrity() -> dict[str, int]:
    """Проверяет целостность данных в БД.
    Перенесено из run_00_recovery.py для централизации.
    """
    stats: dict[str, int] = {
        "total_books": 0,
        "orphaned_sources": 0,
        "orphaned_authors": 0,
        "errors": 0,
    }

    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                # Общее количество книг
                stats["total_books"] = get_books_total_count()

                # Источники без связанных книг (orphaned book_sources)
                cur.execute(
                    """
                    SELECT COUNT(*) as orphaned 
                    FROM book_sources bs 
                    LEFT JOIN books b ON bs.book_id = b.id 
                    WHERE b.id IS NULL
                """
                )
                result = cur.fetchone()
                row_dict = cast(dict[str, Any], result)
                stats["orphaned_sources"] = row_dict["orphaned"]

                # Авторы без связанных книг (orphaned authors)
                cur.execute(
                    """
                    SELECT COUNT(*) as orphaned 
                    FROM authors a 
                    LEFT JOIN book_authors ba ON a.id = ba.author_id 
                    WHERE ba.book_id IS NULL
                """
                )
                result = cur.fetchone()
                row_dict = cast(dict[str, Any], result)
                stats["orphaned_authors"] = row_dict["orphaned"]

                return stats

    except psycopg.Error as e:
        logging.error(f"❌ Ошибка проверки целостности данных: {e}")
        stats["errors"] = 1
        return stats


def add_to_quarantine(source_type: int, source_id: int, quarantine_type: str, reason: str, details: dict) -> None:
    """Добавляет книгу в карантин с записью в PostgreSQL.

    Использует INSERT ... ON CONFLICT DO NOTHING для защиты от повторных записей.
    PostgreSQL становится единственным источником правды о карантинном статусе.

    Args:
        source_type: Тип источника (1=flibusta, 2=searchfloor, 3=anna)
        source_id: ID файла в источнике
        quarantine_type: Основной тип карантина ('trial', 'small_content', 'anthology', etc.)
        reason: Человекочитаемая причина помещения в карантин
        details: JSON с task_data и detected_anomalies

    Raises:
        ProcessingError: При ошибках соединения с БД (тип RETRY)
    """
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    INSERT INTO quarantined_books
                    (source_type, source_id, primary_quarantine_type, reason, details)
                    VALUES (%s, %s, %s, %s, %s)
                    ON CONFLICT (source_type, source_id) DO NOTHING
                    """,
                    (source_type, source_id, quarantine_type, reason, details),
                )

                # Логируем только если была выполнена вставка (не конфликт)
                if cur.rowcount > 0:
                    logging.info(
                        f"📋 Книга {source_type}:{source_id} добавлена в карантин "
                        f"(тип: {quarantine_type})"
                    )
                else:
                    logging.debug(
                        f"📋 Книга {source_type}:{source_id} уже была в карантине "
                        f"(тип: {quarantine_type})"
                    )

    except psycopg.OperationalError as e:
        # Ошибки соединения, блокировки БД - можно повторить
        logging.error(
            f"❌ Ошибка соединения с БД при добавлении в карантин "
            f"source_type={source_type}, source_id={source_id}: {e}"
        )
        raise ProcessingError(
            f"Ошибка соединения с БД при добавлении в карантин {source_type}:{source_id}: {e}",
            error_type=ErrorType.RETRY,
            details={
                "source_type": source_type,
                "source_id": source_id,
                "quarantine_type": quarantine_type,
                "error_category": "connection"
            },
        ) from e
    except psycopg.DataError as e:
        # Ошибки данных (неверные параметры) - не повторять
        logging.error(
            f"❌ Ошибка данных при добавлении в карантин "
            f"source_type={source_type}, source_id={source_id}: {e}"
        )
        raise ProcessingError(
            f"Неверные параметры при добавлении в карантин {source_type}:{source_id}: {e}",
            error_type=ErrorType.FATAL,
            details={
                "source_type": source_type,
                "source_id": source_id,
                "quarantine_type": quarantine_type,
                "error_category": "data"
            },
        ) from e
    except psycopg.Error as e:
        # Остальные ошибки БД - логируем и пробрасываем как RETRY
        logging.error(
            f"❌ Ошибка БД при добавлении в карантин "
            f"source_type={source_type}, source_id={source_id}: {e}"
        )
        raise ProcessingError(
            f"Ошибка БД при добавлении в карантин {source_type}:{source_id}: {e}",
            error_type=ErrorType.RETRY,
            details={
                "source_type": source_type,
                "source_id": source_id,
                "quarantine_type": quarantine_type,
                "error_category": "database"
            },
        ) from e


def get_quarantined_ids(candidate_pairs: list[tuple[int, int]]) -> set[tuple[int, int]]:
    """Проверяет, какие из кандидатов уже находятся в карантине.

    Выполняет пакетный SELECT для оптимизации производительности при больших объемах.
    Используется сканером для фильтрации уже помещенных в карантин книг.

    Args:
        candidate_pairs: Список пар (source_type, source_id) для проверки

    Returns:
        Set пар (source_type, source_id), которые найдены в карантине

    Raises:
        ProcessingError: При ошибках соединения с БД (тип RETRY)
    """
    if not candidate_pairs:
        return set()

    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                # Используем ANY для эффективного пакетного запроса
                cur.execute(
                    """
                    SELECT source_type, source_id
                    FROM quarantined_books
                    WHERE (source_type, source_id) = ANY(%s)
                    """,
                    (candidate_pairs,),
                )

                # Возвращаем set для быстрой проверки принадлежности
                quarantined_pairs = set()
                for row in cur.fetchall():
                    row_dict = cast(dict[str, Any], row)
                    quarantined_pairs.add((row_dict["source_type"], row_dict["source_id"]))

                logging.debug(
                    f"📋 Проверено {len(candidate_pairs)} кандидатов, "
                    f"найдено {len(quarantined_pairs)} в карантине"
                )

                return quarantined_pairs

    except psycopg.OperationalError as e:
        # Ошибки соединения, блокировки БД - можно повторить
        logging.error(f"❌ Ошибка соединения с БД при проверке карантина: {e}")
        raise ProcessingError(
            f"Ошибка соединения с БД при проверке карантина: {e}",
            error_type=ErrorType.RETRY,
            details={"candidates_count": len(candidate_pairs), "error_category": "connection"},
        ) from e
    except psycopg.DataError as e:
        # Ошибки данных (неверные параметры) - не повторять
        logging.error(f"❌ Ошибка данных при проверке карантина: {e}")
        raise ProcessingError(
            f"Неверные параметры при проверке карантина: {e}",
            error_type=ErrorType.FATAL,
            details={"candidates_count": len(candidate_pairs), "error_category": "data"},
        ) from e
    except psycopg.Error as e:
        # Остальные ошибки БД - логируем и пробрасываем как RETRY
        logging.error(f"❌ Ошибка БД при проверке карантина: {e}")
        raise ProcessingError(
            f"Ошибка БД при проверке карантина: {e}",
            error_type=ErrorType.RETRY,
            details={"candidates_count": len(candidate_pairs), "error_category": "database"},
        ) from e
